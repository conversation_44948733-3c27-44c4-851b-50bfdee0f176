services:
  mongodb:
    image: mongo:7.0
    container_name: sinamoa-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: sinamoa-chemicals
    volumes:
      - mongodb_data:/data/db
      - ./blueledger-app/database/init:/docker-entrypoint-initdb.d
    networks:
      - sinamoa-network

  backend:
    build:
      context: ./blueledger-app/backend
      dockerfile: Dockerfile
    container_name: sinamoa-backend
    restart: unless-stopped
    ports:
      - "5000:5000"
      - "5001:5001"
    environment:
      NODE_ENV: production
      MONGODB_URI: ****************************************************************************
      JWT_SECRET: your-super-secure-jwt-secret-key-change-in-production
      JWT_EXPIRE: 24h
      PORT: 5000
      HTTPS_PORT: 5001
      FRONTEND_URL: http://localhost:3000
      BCRYPT_ROUNDS: 12
      SEED_DATABASE: "true"
    depends_on:
      - mongodb
    networks:
      - sinamoa-network
    # SSL certificates generated inside container

  frontend:
    build:
      context: ./blueledger-app/frontend
      dockerfile: Dockerfile
    container_name: sinamoa-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_ENVIRONMENT: production
    depends_on:
      - backend
    networks:
      - sinamoa-network
    # No development volumes needed for production

volumes:
  mongodb_data:

networks:
  sinamoa-network:
    driver: bridge