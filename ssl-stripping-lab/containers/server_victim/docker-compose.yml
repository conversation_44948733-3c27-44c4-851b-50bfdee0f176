version: '3.8'

x-network-config: &network-config
  # This will be dynamically replaced by the network-setup service
  driver: mac<PERSON><PERSON>
  driver_opts:
    parent: ${NETWORK_INTERFACE:-eth0}
  ipam:
    config:
      - subnet: ${NETWORK_SUBNET:-***********/24}
        gateway: ${NETWORK_GATEWAY:-***********}
        ip_range: ${CONTAINER_IP_RANGE:-*************/29}

services:
  network-setup:
    image: alpine:latest
    container_name: network_configurator
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - .:/workspace
    working_dir: /workspace
    command: |
      sh -c "
        echo '🌐 Auto-configuring network settings...'
        
        # Install required tools
        apk add --no-cache docker-cli curl jq
        
        # Get host network info from Docker host
        DEFAULT_GW=\$$(ip route | grep default | head -1 | awk '{print \$$3}')
        INTERFACE=\$$(ip route | grep default | head -1 | awk '{print \$$5}')
        HOST_IP=\$$(ip route get 1 | awk '{print \$$7}' | head -1)
        SUBNET=\$$(echo \$$HOST_IP | cut -d'.' -f1-3).0/24
        CONTAINER_RANGE=\$$(echo \$$HOST_IP | cut -d'.' -f1-3).248/29
        
        echo 'Detected: Interface='\$$INTERFACE' Gateway='\$$DEFAULT_GW' Subnet='\$$SUBNET
        
        # Export as environment variables for other services
        echo \"NETWORK_INTERFACE=\$$INTERFACE\" > .env
        echo \"NETWORK_GATEWAY=\$$DEFAULT_GW\" >> .env
        echo \"NETWORK_SUBNET=\$$SUBNET\" >> .env
        echo \"CONTAINER_IP_RANGE=\$$CONTAINER_RANGE\" >> .env
        
        echo '✅ Network configuration complete!'
        cat .env
      "
    network_mode: host  # Need host network to detect interface info

  ubuntu_server:
    build:
      context: ./ubuntu_server
      dockerfile: Dockerfile
    container_name: blueledger_server
    restart: unless-stopped
    networks:
      - host_network
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    privileged: true
    environment:
      - BLUELEDGER_PATH=/opt/BlueLedger
    depends_on:
      network-setup:
        condition: service_completed_successfully

  debian_victim:
    build:
      context: ./debian_victim  
      dockerfile: Dockerfile
    container_name: ssl_victim
    restart: unless-stopped
    networks:
      - host_network
    depends_on:
      ubuntu_server:
        condition: service_started
      network-setup:
        condition: service_completed_successfully
    environment:
      - TARGET_SERVER_IP=auto
    command: ["python3", "/opt/victim_simulator.py"]

networks:
  host_network:
    <<: *network-config