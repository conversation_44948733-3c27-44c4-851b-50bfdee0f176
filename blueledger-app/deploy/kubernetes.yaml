apiVersion: v1
kind: ConfigMap
metadata:
  name: sinamoa-config
data:
  NODE_ENV: "production"
  MONGODB_URI: "******************************************************************************"
  JWT_SECRET: "your-super-secure-jwt-secret-key-change-in-production"
  JWT_EXPIRE: "24h"
  PORT: "5000"
  HTTPS_PORT: "5001"
  FRONTEND_URL: "http://localhost:3000"
  BCRYPT_ROUNDS: "12"
  SEED_DATABASE: "true"
  REACT_APP_API_URL: "http://localhost:5000/api"
  REACT_APP_ENVIRONMENT: "production"
  MONGO_INITDB_ROOT_USERNAME: "admin"
  MONGO_INITDB_ROOT_PASSWORD: "password123"
  MONGO_INITDB_DATABASE: "sinamoa-chemicals"

---
apiVersion: v1
kind: Pod
metadata:
  name: sinamoa-app
  labels:
    app: sinamoa
spec:
  containers:
  - name: mongodb
    image: docker.io/mongo:7.0
    ports:
    - containerPort: 27017
      hostPort: 27017
    env:
    - name: MONGO_INITDB_ROOT_USERNAME
      valueFrom:
        configMapKeyRef:
          name: sinamoa-config
          key: MONGO_INITDB_ROOT_USERNAME
    - name: MONGO_INITDB_ROOT_PASSWORD
      valueFrom:
        configMapKeyRef:
          name: sinamoa-config
          key: MONGO_INITDB_ROOT_PASSWORD
    - name: MONGO_INITDB_DATABASE
      valueFrom:
        configMapKeyRef:
          name: sinamoa-config
          key: MONGO_INITDB_DATABASE
    volumeMounts:
    - name: mongodb-data
      mountPath: /data/db
    - name: database-init
      mountPath: /docker-entrypoint-initdb.d

  - name: backend
    image: localhost/sinamoa-backend:latest
    ports:
    - containerPort: 5000
      hostPort: 5000
    - containerPort: 5001
      hostPort: 5001
    envFrom:
    - configMapRef:
        name: sinamoa-config
    volumeMounts:
    - name: ssl-cert
      mountPath: /app/cert.pem
      readOnly: true
    - name: ssl-key
      mountPath: /app/key.pem
      readOnly: true

  - name: frontend
    image: localhost/sinamoa-frontend:latest
    ports:
    - containerPort: 3000
      hostPort: 3000
    envFrom:
    - configMapRef:
        name: sinamoa-config

  volumes:
  - name: mongodb-data
    hostPath:
      path: ./mongodb-data
      type: DirectoryOrCreate
  - name: database-init
    hostPath:
      path: ./database/init
      type: Directory
  - name: ssl-cert
    hostPath:
      path: ./backend/cert.pem
      type: File
  - name: ssl-key
    hostPath:
      path: ./backend/key.pem
      type: File

---
apiVersion: v1
kind: Service
metadata:
  name: sinamoa-service
spec:
  selector:
    app: sinamoa
  ports:
  - name: frontend
    port: 3000
    targetPort: 3000
    hostPort: 3000
  - name: backend-http
    port: 5000
    targetPort: 5000
    hostPort: 5000
  - name: backend-https
    port: 5001
    targetPort: 5001
    hostPort: 5001
  - name: mongodb
    port: 27017
    targetPort: 27017
    hostPort: 27017
  type: ClusterIP
