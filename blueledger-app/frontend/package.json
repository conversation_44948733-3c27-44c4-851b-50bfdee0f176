{"name": "sinamoa-chemicals-frontend", "version": "1.0.0", "description": "Frontend React application for Sinamoa Chemicals", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "react-query": "^3.39.3", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "styled-components": "^6.0.7", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.3.1", "yup": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-scripts": "5.0.1", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7"}}