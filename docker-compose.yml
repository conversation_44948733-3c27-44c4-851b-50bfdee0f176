version: '3.8'

# Bridge network for server and database
x-bridge-config: &bridge-config
  driver: bridge

# Macvlan network for victim to get real router DHCP IP
x-macvlan-config: &macvlan-config
  driver: macvlan
  driver_opts:
    parent: ${NETWORK_INTERFACE:-eth0}
  ipam:
    config:
      - subnet: ${NETWORK_SUBNET:-***********/24}
        gateway: ${NETWORK_GATEWAY:-***********}

services:
  # Network auto-configuration service - runs first  
  network-setup:
    image: alpine:latest
    container_name: ssl_lab_network_config
    volumes:
      - .:/workspace
    working_dir: /workspace
    command: |
      sh -c "
        echo '🌐 SSL Stripping Lab - Network config...'
        echo '⚠️  Please run ./setup-macvlan.sh on the host first!'
        echo '✅ Checking for .env file...'
        if [ -f .env ]; then
          echo '✅ Network configuration found!'
        else
          echo '❌ No .env file found. Run ./setup-macvlan.sh first!'
          exit 1
        fi
      "

  # MongoDB database
  mongodb:
    image: mongo:7
    container_name: blueledger_mongodb
    restart: unless-stopped
    networks:
      - bridge_network
    environment:
      - MONGO_INITDB_DATABASE=blueledger
    depends_on:
      network-setup:
        condition: service_completed_successfully

  # BlueLedger server - uses bridge network, accessible via host ports
  blueledger-server:
    build:
      context: .
      dockerfile: ssl-stripping-lab/containers/server_victim/ubuntu_server/Dockerfile
    container_name: blueledger_server
    restart: unless-stopped
    networks:
      - bridge_network
    ports:
      - "3000:3000"  # Frontend
      - "5000:5000"  # HTTP API
      - "5001:5001"  # HTTPS API
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    privileged: true
    environment:
      - BLUELEDGER_PATH=/opt/BlueLedger
      - MONGODB_URI=mongodb://blueledger_mongodb:27017/blueledger
      - JWT_SECRET=your-super-secure-jwt-secret-key-change-in-production
      - JWT_EXPIRE=24h
      - NODE_ENV=development
      - SEED_DATABASE=true
      - BCRYPT_ROUNDS=12
      - PORT=5000
      - HTTPS_PORT=5001
      - FRONTEND_URL=http://localhost:3000
    depends_on:
      mongodb:
        condition: service_started
      network-setup:
        condition: service_completed_successfully

  # Victim simulator - gets real DHCP IP from router (different from host)
  ssl-victim:
    build:
      context: ./ssl-stripping-lab/containers/server_victim/debian_victim
      dockerfile: Dockerfile
    container_name: ssl_victim
    restart: unless-stopped
    networks:
      - macvlan_network
    depends_on:
      blueledger-server:
        condition: service_started
      network-setup:
        condition: service_completed_successfully
    environment:
      - TARGET_SERVER_IP=auto
    command: ["python3", "/opt/victim_simulator.py"]

  # IP display service - runs after everything is up
  show-ips:
    image: alpine:latest
    container_name: ssl_lab_ip_display
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - .:/workspace
    working_dir: /workspace
    command: |
      sh -c "
        # Install required tools
        apk add --no-cache docker-cli jq
        
        # Wait for containers to be fully running
        echo ''
        echo '⏳ Waiting for containers to get IP addresses...'
        sleep 25
        
        echo ''
        echo '🎯 SSL STRIPPING LAB - CONTAINER IPS:'
        echo '=================================='
        
        # Get container IPs with retry logic
        for i in \$$(seq 1 10); do
          # Server uses host networking, so use host IP
          SERVER_IP=\$$(hostname -I | awk '{print \$$1}' | head -1)

          # Victim uses macvlan network, get its actual IP
          VICTIM_IP=\$$(docker inspect ssl_victim 2>/dev/null | jq -r '.[0].NetworkSettings.Networks.blueledger_macvlan_network.IPAddress' 2>/dev/null || echo 'null')

          # Fallback: if victim IP is null, try to get any macvlan IP
          if [ \"\$$VICTIM_IP\" = 'null' ] || [ \"\$$VICTIM_IP\" = '' ]; then
            VICTIM_IP=\$$(docker inspect ssl_victim 2>/dev/null | jq -r '.[0].NetworkSettings.Networks | to_entries[] | select(.key | contains(\"macvlan\")) | .value.IPAddress' 2>/dev/null | head -1 || echo 'null')
          fi

          if [ \"\$$SERVER_IP\" != 'null' ] && [ \"\$$VICTIM_IP\" != 'null' ] && [ \"\$$SERVER_IP\" != '' ] && [ \"\$$VICTIM_IP\" != '' ]; then
            break
          fi
          echo '   Still waiting... (attempt '\$$i'/10)'
          sleep 3
        done
        
        echo \"📱 BlueLedger Server: \$$SERVER_IP\"
        echo \"🎭 SSL Victim: \$$VICTIM_IP\"
        echo ''
        
        # Show access URLs if server is running
        if [ \"\$$SERVER_IP\" != 'null' ] && [ \"\$$SERVER_IP\" != '' ]; then
          echo '🌐 Access URLs:'
          echo \"   Frontend:  http://\$$SERVER_IP:3000\"
          echo \"   HTTP API:  http://\$$SERVER_IP:5000\"
          echo \"   HTTPS API: https://\$$SERVER_IP:5001\"
          echo ''
          echo '📝 Note: Server uses host networking - accessible via host IP'
          echo ''
        fi
        
        echo '🔓 Ready for SSL Stripping Attack!'
        echo 'Use these IPs in your ettercap and iptables commands.'
        echo '=================================='
        echo ''
        
        # Show quick ettercap command template
        if [ \"\$$SERVER_IP\" != 'null' ] && [ \"\$$VICTIM_IP\" != 'null' ] && [ \"\$$SERVER_IP\" != '' ] && [ \"\$$VICTIM_IP\" != '' ]; then
          GATEWAY=\$$(ip route | grep default | head -1 | awk '{print \$$3}')
          echo '💡 Quick SSL Stripping Commands:'
          echo \"   ettercap: sudo ettercap -T -M arp:remote /\$$VICTIM_IP// /\$$GATEWAY//\"
          echo \"   iptables: sudo iptables -t nat -A PREROUTING -s \$$VICTIM_IP -p tcp --dport 5001 -j REDIRECT --to-port 8080\"
          echo ''
          echo '📚 Complete guide: ssl-stripping-lab/SSL_Stripping_Lab_Guide_NEW.md'
        fi
        echo ''
      "
    depends_on:
      blueledger-server:
        condition: service_started
      ssl-victim:
        condition: service_started
    network_mode: host

networks:
  # Bridge network for server and database
  bridge_network:
    <<: *bridge-config

  # Macvlan network for victim to get real router IP
  macvlan_network:
    <<: *macvlan-config